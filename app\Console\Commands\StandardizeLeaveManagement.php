<?php

namespace App\Console\Commands;

use App\Models\Company;
use App\Models\CompanyLeaveType;
use App\Models\CompanyLeaveTypePolicy;
use App\Models\CompanyDefaultLeaveType;
use App\Models\EmployeeLeaveBalance;
use App\Models\EmployeeLeaveRequest;
use App\Models\Employee;
use App\Repositories\Repository;
use App\Repositories\V1\Leaves\CompanyDefaultLeaveTypesRepository;
use App\Services\V1\LeaveManagement\FillEmployeeBalancesService;
use App\Traits\GenerateUuid;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Exception;

class StandardizeLeaveManagement extends Command
{
    use GenerateUuid;

    protected $signature = 'leave:standardize 
                            {--company-id= : Process specific company ID only}
                            {--dry-run : Preview changes without applying them}
                            {--chunk=50 : Number of companies to process per batch}';

    protected $description = 'Standardize leave management system by ensuring each company has exactly one annual and one emergency leave type';

    private $companyLeaveTypeRepository;
    private $companyLeaveTypePolicyRepository;
    private $companyDefaultLeaveTypesRepository;
    private $fillEmployeeBalancesService;
    
    private $stats = [
        'companies_processed' => 0,
        'annual_leaves_created' => 0,
        'annual_leaves_cleaned' => 0,
        'emergency_leaves_created' => 0,
        'emergency_leaves_cleaned' => 0,
        'leave_requests_redirected' => 0,
        'employee_balances_created' => 0,
        'employee_balances_deleted' => 0,
        'policies_updated' => 0,
    ];

    private $detailedLog = [];

    public function __construct()
    {
        parent::__construct();
        $this->companyLeaveTypeRepository = Repository::getRepository('CompanyLeaveType');
        $this->companyLeaveTypePolicyRepository = Repository::getRepository('CompanyLeaveTypePolicy');
        $this->companyDefaultLeaveTypesRepository = new CompanyDefaultLeaveTypesRepository();
        $this->fillEmployeeBalancesService = app(FillEmployeeBalancesService::class);
    }

    public function handle()
    {
        $this->info('Starting Leave Management Standardization...');
        
        if ($this->option('dry-run')) {
            $this->warn('DRY RUN MODE - No changes will be applied');
        }

        try {
            DB::beginTransaction();

            $companies = $this->getCompaniesToProcess();
            $this->info("Found {$companies->count()} companies to process");

            $companies->chunk($this->option('chunk'))->each(function ($companyChunk) {
                foreach ($companyChunk as $company) {
                    $this->processCompany($company);
                }
            });

            if ($this->option('dry-run')) {
                DB::rollBack();
                $this->warn('DRY RUN COMPLETED - No changes were applied');
            } else {
                DB::commit();
                $this->info('All changes committed successfully');
            }

            $this->displayStats();

        } catch (Exception $e) {
            DB::rollBack();
            $this->error("Error occurred: " . $e->getMessage());
            Log::error('Leave standardization failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }

        return 0;
    }

    private function getCompaniesToProcess()
    {
        $query = Company::where('status', 'active')
            ->with(['annualLeaveType', 'emergencyLeaveType', 'companyLeaveTypes', 'titles']);

        if ($companyId = $this->option('company-id')) {
            $query->where('id', $companyId);
        }

        return $query->get();
    }

    private function processCompany(Company $company)
    {
        $this->info("Processing Company ID: {$company->id} - {$company->name}");

        $companyLog = [
            'company_id' => $company->id,
            'company_name' => $company->name,
            'actions' => [],
            'errors' => [],
            'start_time' => now(),
        ];

        try {
            // Step 1: Standardize Annual Leave
            $primaryAnnualLeave = $this->standardizeAnnualLeave($company);
            $companyLog['actions'][] = "Annual leave standardized: ID {$primaryAnnualLeave->id}";

            // Step 2: Standardize Emergency Leave
            $primaryEmergencyLeave = $this->standardizeEmergencyLeave($company, $primaryAnnualLeave);
            $companyLog['actions'][] = "Emergency leave standardized: ID {$primaryEmergencyLeave->id}";

            // Step 3: Update policy base balances
            $this->updatePolicyBalances($primaryAnnualLeave, $primaryEmergencyLeave);
            $companyLog['actions'][] = "Policy balances updated";

            $this->stats['companies_processed']++;
            $companyLog['status'] = 'success';
            $companyLog['end_time'] = now();

            $this->info("✓ Company {$company->id} processed successfully");

            // Log to application log
            Log::info("Company standardization completed", [
                'company_id' => $company->id,
                'annual_leave_id' => $primaryAnnualLeave->id,
                'emergency_leave_id' => $primaryEmergencyLeave->id,
                'dry_run' => $this->option('dry-run')
            ]);

        } catch (Exception $e) {
            $companyLog['status'] = 'failed';
            $companyLog['errors'][] = $e->getMessage();
            $companyLog['end_time'] = now();

            $this->error("✗ Failed to process company {$company->id}: " . $e->getMessage());
            Log::error("Company processing failed", [
                'company_id' => $company->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        } finally {
            $this->detailedLog[] = $companyLog;
        }
    }

    private function standardizeAnnualLeave(Company $company): CompanyLeaveType
    {
        $this->info("  → Standardizing Annual Leave for company {$company->id}");

        // Step 1: Check if company has valid annual_leave_id
        $primaryAnnualLeave = null;
        if ($company->annual_leave_id) {
            $primaryAnnualLeave = CompanyLeaveType::where('id', $company->annual_leave_id)
                ->where('company_id', $company->id)
                ->whereNull('deleted_at')
                ->first();
        }

        // Step 2: If no valid annual leave, find or create one
        if (!$primaryAnnualLeave) {
            $primaryAnnualLeave = $this->findOrCreateAnnualLeave($company);

            // Update company's annual_leave_id
            if (!$this->option('dry-run')) {
                $company->update(['annual_leave_id' => $primaryAnnualLeave->id]);
            }
            $this->info("    ✓ Set company annual_leave_id to {$primaryAnnualLeave->id}");
        }

        // Step 3: Ensure policy exists
        $this->ensureAnnualLeavePolicy($company, $primaryAnnualLeave);

        // Step 4: Ensure default leave type record exists
        $this->ensureDefaultLeaveTypeRecord($company, $primaryAnnualLeave, 'annual_leave_type_id');

        // Step 5: Ensure employee balances exist
        $this->ensureEmployeeBalances($company, $primaryAnnualLeave);

        // Step 6: Clean up duplicate annual leaves
        $this->cleanupDuplicateAnnualLeaves($company, $primaryAnnualLeave);

        return $primaryAnnualLeave;
    }

    private function findOrCreateAnnualLeave(Company $company): CompanyLeaveType
    {
        // First, try to find existing annual leave
        $existingAnnualLeave = CompanyLeaveType::where('company_id', $company->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%annual%')
                      ->orWhere('name_ar', 'LIKE', '%سنوية%')
                      ->orWhere('name_en', 'LIKE', '%Annual%')
                      ->orWhere('name_ar', 'LIKE', '%سنوي%');
            })
            ->first();

        if ($existingAnnualLeave) {
            $this->info("    ✓ Found existing annual leave: {$existingAnnualLeave->id}");
            return $existingAnnualLeave;
        }

        // Create new annual leave
        $annualLeaveData = [
            'company_id' => $company->id,
            'name_en' => 'Annual Leave',
            'name_ar' => 'إجازة سنوية',
            'is_primary' => 1,
            'balance_period' => config('globals.BALANCE_PERIODS.CALENDAR_YEAR'),
            'gender' => 'all',
            'uuid' => $this->generateUuid(),
        ];

        if (!$this->option('dry-run')) {
            $annualLeave = CompanyLeaveType::create($annualLeaveData);
        } else {
            $annualLeave = new CompanyLeaveType($annualLeaveData);
            $annualLeave->id = 999999; // Dummy ID for dry run
        }

        $this->stats['annual_leaves_created']++;
        $this->info("    ✓ Created new annual leave: {$annualLeave->id}");

        return $annualLeave;
    }

    private function ensureAnnualLeavePolicy(Company $company, CompanyLeaveType $annualLeave)
    {
        $existingPolicy = CompanyLeaveTypePolicy::where('company_leave_type_id', $annualLeave->id)
            ->where('company_id', $company->id)
            ->whereNull('deleted_at')
            ->first();

        if (!$existingPolicy) {
            $policyData = [
                'company_id' => $company->id,
                'company_leave_type_id' => $annualLeave->id,
                'request_before_days' => 0,
                'min_requester_years' => 0,
                'base_balance' => 168, // Will be updated later if different
                'is_probation_allowed' => 0,
            ];

            if (!$this->option('dry-run')) {
                $policy = CompanyLeaveTypePolicy::create($policyData);
                // Attach to all company titles
                $policy->titles()->attach($company->titles->pluck('id'));
            }

            $this->info("    ✓ Created annual leave policy");
        } else {
            // Ensure policy is attached to all titles
            if (!$this->option('dry-run')) {
                $existingPolicy->titles()->syncWithoutDetaching($company->titles->pluck('id'));
            }
            $this->info("    ✓ Annual leave policy exists and titles synced");
        }
    }

    private function ensureDefaultLeaveTypeRecord(Company $company, CompanyLeaveType $leaveType, string $key)
    {
        $existingDefault = CompanyDefaultLeaveType::where('company_id', $company->id)
            ->where('key', $key)
            ->first();

        if (!$existingDefault) {
            $defaultData = [
                'company_id' => $company->id,
                'company_leave_type_id' => $leaveType->id,
                'key' => $key,
            ];

            if (!$this->option('dry-run')) {
                $this->companyDefaultLeaveTypesRepository->add($defaultData);
            }

            $this->info("    ✓ Created default leave type record for {$key}");
        } else if ($existingDefault->company_leave_type_id !== $leaveType->id) {
            if (!$this->option('dry-run')) {
                $existingDefault->update(['company_leave_type_id' => $leaveType->id]);
            }
            $this->info("    ✓ Updated default leave type record for {$key}");
        }
    }

    private function ensureEmployeeBalances(Company $company, CompanyLeaveType $leaveType)
    {
        $nonTerminatedEmployees = Employee::where('company_id', $company->id)
            ->where('status', 'active')
            ->whereDoesntHave('employeeInfo', function ($query) {
                $query->whereNotNull('termination_date');
            })
            ->get();

        $policy = $leaveType->companyLeaveTypePolicy;
        if (!$policy) {
            $this->warn("    ⚠ No policy found for leave type {$leaveType->id}, skipping balance creation");
            return;
        }

        $balancesCreated = 0;
        foreach ($nonTerminatedEmployees as $employee) {
            $existingBalance = EmployeeLeaveBalance::where('employee_id', $employee->id)
                ->where('company_leave_type_id', $leaveType->id)
                ->where('start', '>=', now()->startOfYear())
                ->where('end', '<=', now()->endOfYear())
                ->first();

            if (!$existingBalance && !$this->option('dry-run')) {
                EmployeeLeaveBalance::create([
                    'employee_id' => $employee->id,
                    'company_leave_type_id' => $leaveType->id,
                    'company_leave_type_policy_id' => $policy->id,
                    'balance' => $policy->base_balance,
                    'start' => now()->startOfYear(),
                    'end' => now()->endOfYear(),
                ]);
                $balancesCreated++;
            } elseif (!$existingBalance) {
                $balancesCreated++; // Count for dry run
            }
        }

        if ($balancesCreated > 0) {
            $this->stats['employee_balances_created'] += $balancesCreated;
            $this->info("    ✓ Created {$balancesCreated} employee balances");
        }
    }

    private function cleanupDuplicateAnnualLeaves(Company $company, CompanyLeaveType $primaryAnnualLeave)
    {
        $duplicateAnnualLeaves = CompanyLeaveType::where('company_id', $company->id)
            ->where('id', '!=', $primaryAnnualLeave->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%annual%')
                      ->orWhere('name_ar', 'LIKE', '%سنوية%')
                      ->orWhere('name_en', 'LIKE', '%Annual%')
                      ->orWhere('name_ar', 'LIKE', '%سنوي%');
            })
            ->get();

        foreach ($duplicateAnnualLeaves as $duplicateLeave) {
            $this->info("    → Cleaning up duplicate annual leave: {$duplicateLeave->id}");

            // Redirect leave requests
            $this->redirectLeaveRequests($duplicateLeave, $primaryAnnualLeave);

            // Soft delete employee balances
            $this->softDeleteEmployeeBalances($duplicateLeave);

            // Delete policies
            $this->deleteLeaveTypePolicies($duplicateLeave);

            // Delete the leave type itself
            if (!$this->option('dry-run')) {
                $duplicateLeave->delete();
            }

            $this->stats['annual_leaves_cleaned']++;
        }

        if ($duplicateAnnualLeaves->count() > 0) {
            $this->info("    ✓ Cleaned up {$duplicateAnnualLeaves->count()} duplicate annual leaves");
        }
    }

    private function redirectLeaveRequests(CompanyLeaveType $fromLeaveType, CompanyLeaveType $toLeaveType)
    {
        $leaveRequests = EmployeeLeaveRequest::where('company_leave_type_id', $fromLeaveType->id)->get();

        $redirected = 0;
        foreach ($leaveRequests as $request) {
            if (!$this->option('dry-run')) {
                $request->update([
                    'company_leave_type_id' => $toLeaveType->id,
                    'company_leave_type_policy_id' => $toLeaveType->companyLeaveTypePolicy->id ?? $request->company_leave_type_policy_id,
                ]);
            }
            $redirected++;
        }

        if ($redirected > 0) {
            $this->stats['leave_requests_redirected'] += $redirected;
            $this->info("      ✓ Redirected {$redirected} leave requests");
        }
    }

    private function softDeleteEmployeeBalances(CompanyLeaveType $leaveType)
    {
        $balances = EmployeeLeaveBalance::where('company_leave_type_id', $leaveType->id)->get();

        $deleted = 0;
        foreach ($balances as $balance) {
            if (!$this->option('dry-run')) {
                $balance->delete(); // Soft delete
            }
            $deleted++;
        }

        if ($deleted > 0) {
            $this->stats['employee_balances_deleted'] += $deleted;
            $this->info("      ✓ Soft deleted {$deleted} employee balances");
        }
    }

    private function deleteLeaveTypePolicies(CompanyLeaveType $leaveType)
    {
        $policies = CompanyLeaveTypePolicy::where('company_leave_type_id', $leaveType->id)->get();

        foreach ($policies as $policy) {
            if (!$this->option('dry-run')) {
                // Detach titles first
                $policy->titles()->detach();
                // Delete policy
                $policy->delete();
            }
        }

        if ($policies->count() > 0) {
            $this->info("      ✓ Deleted {$policies->count()} policies");
        }
    }

    private function standardizeEmergencyLeave(Company $company, CompanyLeaveType $annualLeave): CompanyLeaveType
    {
        $this->info("  → Standardizing Emergency Leave for company {$company->id}");

        // Step 1: Try to find emergency leave linked to annual leave
        $primaryEmergencyLeave = $this->findEmergencyLeaveLinkedToAnnual($company, $annualLeave);

        // Step 2: If not found, find existing emergency leave and link it
        if (!$primaryEmergencyLeave) {
            $primaryEmergencyLeave = $this->findOrCreateEmergencyLeave($company, $annualLeave);
        }

        // Step 3: Ensure policy exists
        $this->ensureEmergencyLeavePolicy($company, $primaryEmergencyLeave);

        // Step 4: Ensure default leave type record exists
        $this->ensureDefaultLeaveTypeRecord($company, $primaryEmergencyLeave, 'emergency_leave_type_id');

        // Step 5: Clean up duplicate emergency leaves
        $this->cleanupDuplicateEmergencyLeaves($company, $primaryEmergencyLeave);

        return $primaryEmergencyLeave;
    }

    private function findEmergencyLeaveLinkedToAnnual(Company $company, CompanyLeaveType $annualLeave): ?CompanyLeaveType
    {
        return CompanyLeaveType::where('company_id', $company->id)
            ->where('secondary_company_leave_type_id', $annualLeave->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%emergency%')
                      ->orWhere('name_ar', 'LIKE', '%طارئة%')
                      ->orWhere('name_en', 'LIKE', '%Emergency%')
                      ->orWhere('name_ar', 'LIKE', '%عارضة%');
            })
            ->first();
    }

    private function findOrCreateEmergencyLeave(Company $company, CompanyLeaveType $annualLeave): CompanyLeaveType
    {
        // First, try to find existing emergency leave
        $existingEmergencyLeaves = CompanyLeaveType::where('company_id', $company->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%emergency%')
                      ->orWhere('name_ar', 'LIKE', '%طارئة%')
                      ->orWhere('name_en', 'LIKE', '%Emergency%')
                      ->orWhere('name_ar', 'LIKE', '%عارضة%');
            })
            ->get();

        // Prefer one that has a default leave type record
        $preferredEmergencyLeave = null;
        foreach ($existingEmergencyLeaves as $emergencyLeave) {
            $hasDefaultRecord = CompanyDefaultLeaveType::where('company_id', $company->id)
                ->where('company_leave_type_id', $emergencyLeave->id)
                ->where('key', 'emergency_leave_type_id')
                ->exists();

            if ($hasDefaultRecord) {
                $preferredEmergencyLeave = $emergencyLeave;
                break;
            }
        }

        // If no preferred one, take the first
        if (!$preferredEmergencyLeave && $existingEmergencyLeaves->isNotEmpty()) {
            $preferredEmergencyLeave = $existingEmergencyLeaves->first();
        }

        if ($preferredEmergencyLeave) {
            // Link it to annual leave
            if (!$this->option('dry-run')) {
                $preferredEmergencyLeave->update([
                    'secondary_company_leave_type_id' => $annualLeave->id,
                    'is_primary' => 0
                ]);
            }
            $this->info("    ✓ Found and linked existing emergency leave: {$preferredEmergencyLeave->id}");
            return $preferredEmergencyLeave;
        }

        // Create new emergency leave
        $emergencyLeaveData = [
            'company_id' => $company->id,
            'name_en' => 'Emergency Leave',
            'name_ar' => 'إجازة طارئة',
            'is_primary' => 0,
            'secondary_company_leave_type_id' => $annualLeave->id,
            'balance_period' => $annualLeave->balance_period, // Match annual leave period
            'gender' => 'all',
            'uuid' => $this->generateUuid(),
        ];

        if (!$this->option('dry-run')) {
            $emergencyLeave = CompanyLeaveType::create($emergencyLeaveData);
        } else {
            $emergencyLeave = new CompanyLeaveType($emergencyLeaveData);
            $emergencyLeave->id = 999998; // Dummy ID for dry run
        }

        $this->stats['emergency_leaves_created']++;
        $this->info("    ✓ Created new emergency leave: {$emergencyLeave->id}");

        return $emergencyLeave;
    }

    private function ensureEmergencyLeavePolicy(Company $company, CompanyLeaveType $emergencyLeave)
    {
        $existingPolicy = CompanyLeaveTypePolicy::where('company_leave_type_id', $emergencyLeave->id)
            ->where('company_id', $company->id)
            ->whereNull('deleted_at')
            ->first();

        if (!$existingPolicy) {
            $policyData = [
                'company_id' => $company->id,
                'company_leave_type_id' => $emergencyLeave->id,
                'request_before_days' => 0,
                'min_requester_years' => 0,
                'base_balance' => 48, // Will be updated later if different
                'is_probation_allowed' => 0,
            ];

            if (!$this->option('dry-run')) {
                $policy = CompanyLeaveTypePolicy::create($policyData);
                // Attach to all company titles
                $policy->titles()->attach($company->titles->pluck('id'));
            }

            $this->info("    ✓ Created emergency leave policy");
        } else {
            // Ensure policy is attached to all titles
            if (!$this->option('dry-run')) {
                $existingPolicy->titles()->syncWithoutDetaching($company->titles->pluck('id'));
            }
            $this->info("    ✓ Emergency leave policy exists and titles synced");
        }
    }

    private function cleanupDuplicateEmergencyLeaves(Company $company, CompanyLeaveType $primaryEmergencyLeave)
    {
        $duplicateEmergencyLeaves = CompanyLeaveType::where('company_id', $company->id)
            ->where('id', '!=', $primaryEmergencyLeave->id)
            ->whereNull('deleted_at')
            ->where(function ($query) {
                $query->where('name_en', 'LIKE', '%emergency%')
                      ->orWhere('name_ar', 'LIKE', '%طارئة%')
                      ->orWhere('name_en', 'LIKE', '%Emergency%')
                      ->orWhere('name_ar', 'LIKE', '%عارضة%');
            })
            ->get();

        foreach ($duplicateEmergencyLeaves as $duplicateLeave) {
            $this->info("    → Cleaning up duplicate emergency leave: {$duplicateLeave->id}");

            // Redirect leave requests
            $this->redirectLeaveRequests($duplicateLeave, $primaryEmergencyLeave);

            // Soft delete employee balances
            $this->softDeleteEmployeeBalances($duplicateLeave);

            // Delete policies and default records
            $this->deleteLeaveTypePolicies($duplicateLeave);
            $this->deleteDefaultLeaveTypeRecords($duplicateLeave);

            // Delete the leave type itself
            if (!$this->option('dry-run')) {
                $duplicateLeave->delete();
            }

            $this->stats['emergency_leaves_cleaned']++;
        }

        if ($duplicateEmergencyLeaves->count() > 0) {
            $this->info("    ✓ Cleaned up {$duplicateEmergencyLeaves->count()} duplicate emergency leaves");
        }
    }

    private function deleteDefaultLeaveTypeRecords(CompanyLeaveType $leaveType)
    {
        $defaultRecords = CompanyDefaultLeaveType::where('company_leave_type_id', $leaveType->id)->get();

        foreach ($defaultRecords as $record) {
            if (!$this->option('dry-run')) {
                $record->delete();
            }
        }

        if ($defaultRecords->count() > 0) {
            $this->info("      ✓ Deleted {$defaultRecords->count()} default leave type records");
        }
    }

    private function updatePolicyBalances(CompanyLeaveType $annualLeave, CompanyLeaveType $emergencyLeave)
    {
        $this->info("  → Updating policy base balances");

        // Update annual leave policy to 168 hours
        $annualPolicy = $annualLeave->companyLeaveTypePolicy;
        if ($annualPolicy && $annualPolicy->base_balance != 168) {
            if (!$this->option('dry-run')) {
                $annualPolicy->update(['base_balance' => 168]);
            }
            $this->stats['policies_updated']++;
            $this->info("    ✓ Updated annual leave policy base balance to 168 hours");
        }

        // Update emergency leave policy to 48 hours
        $emergencyPolicy = $emergencyLeave->companyLeaveTypePolicy;
        if ($emergencyPolicy && $emergencyPolicy->base_balance != 48) {
            if (!$this->option('dry-run')) {
                $emergencyPolicy->update(['base_balance' => 48]);
            }
            $this->stats['policies_updated']++;
            $this->info("    ✓ Updated emergency leave policy base balance to 48 hours");
        }
    }

    private function displayStats()
    {
        $this->info("\n=== STANDARDIZATION SUMMARY ===");
        foreach ($this->stats as $key => $value) {
            $this->info(ucwords(str_replace('_', ' ', $key)) . ": {$value}");
        }

        // Display detailed company log
        $this->info("\n=== DETAILED COMPANY LOG ===");
        foreach ($this->detailedLog as $log) {
            $status = $log['status'] === 'success' ? '✓' : '✗';
            $this->info("{$status} Company {$log['company_id']} ({$log['company_name']}):");

            if (!empty($log['actions'])) {
                foreach ($log['actions'] as $action) {
                    $this->info("    → {$action}");
                }
            }

            if (!empty($log['errors'])) {
                foreach ($log['errors'] as $error) {
                    $this->error("    ✗ {$error}");
                }
            }

            $duration = $log['end_time']->diffInSeconds($log['start_time']);
            $this->info("    Duration: {$duration}s");
        }

        // Summary by status
        $successful = collect($this->detailedLog)->where('status', 'success')->count();
        $failed = collect($this->detailedLog)->where('status', 'failed')->count();

        $this->info("\n=== PROCESSING SUMMARY ===");
        $this->info("Successful: {$successful}");
        $this->info("Failed: {$failed}");
        $this->info("Total: " . ($successful + $failed));

        // Save detailed log to file
        $this->saveDetailedLogToFile();

        $this->info("\n=== RECOMMENDATIONS ===");
        $this->info("1. Review the changes in a staging environment before running in production");
        $this->info("2. Consider running with --dry-run first to preview changes");
        $this->info("3. Monitor employee leave balances after the changes");
        $this->info("4. Verify that leave requests are properly redirected");
        $this->info("5. Check that all companies have proper annual and emergency leave setup");
        $this->info("6. Review the detailed log file for complete processing information");
    }

    private function saveDetailedLogToFile()
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "leave_standardization_{$timestamp}.json";
        $filepath = storage_path("logs/{$filename}");

        $logData = [
            'execution_time' => now(),
            'dry_run' => $this->option('dry-run'),
            'stats' => $this->stats,
            'companies' => $this->detailedLog,
        ];

        file_put_contents($filepath, json_encode($logData, JSON_PRETTY_PRINT));
        $this->info("Detailed log saved to: {$filepath}");
    }
}
